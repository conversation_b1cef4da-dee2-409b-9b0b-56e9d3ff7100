import { supabase } from './supabase';

/**
 * نتيجة فحص حدود طلبات تحديث معلومات التواصل
 */
export interface RateLimitCheckResult {
  allowed: boolean;
  daily_requests_count: number;
  daily_limit: number;
  wait_minutes: number;
  blocked_until: string | null;
  message: string;
}

/**
 * نتيجة تسجيل طلب جديد
 */
export interface RecordRequestResult {
  success: boolean;
  daily_requests_count: number;
  daily_limit: number;
  next_allowed_at: string;
  is_blocked_until: string | null;
  message: string;
}

/**
 * خدمة إدارة حدود طلبات تحديث معلومات التواصل
 */
export class ContactUpdateRateLimitService {
  
  /**
   * فحص ما إذا كان المستخدم مسموح له بإرسال طلب تحديث معلومات التواصل
   * @param userId معرف المستخدم
   * @returns نتيجة الفحص
   */
  static async checkRateLimit(userId: string): Promise<RateLimitCheckResult> {
    try {
      console.log('🔍 Checking contact update rate limit for user:', userId);
      
      const { data, error } = await supabase.rpc('check_contact_update_rate_limit', {
        p_user_id: userId
      });

      if (error) {
        console.error('❌ Error checking rate limit:', error);
        throw new Error(`خطأ في فحص حدود الطلبات: ${error.message}`);
      }

      console.log('✅ Rate limit check result:', data);
      return data as RateLimitCheckResult;
    } catch (error) {
      console.error('❌ Error in checkRateLimit:', error);
      throw error;
    }
  }

  /**
   * تسجيل طلب تحديث معلومات التواصل جديد
   * @param userId معرف المستخدم
   * @returns نتيجة التسجيل
   */
  static async recordRequest(userId: string): Promise<RecordRequestResult> {
    try {
      console.log('📝 Recording contact update request for user:', userId);
      
      const { data, error } = await supabase.rpc('record_contact_update_request', {
        p_user_id: userId
      });

      if (error) {
        console.error('❌ Error recording request:', error);
        throw new Error(`خطأ في تسجيل الطلب: ${error.message}`);
      }

      console.log('✅ Request recorded successfully:', data);
      return data as RecordRequestResult;
    } catch (error) {
      console.error('❌ Error in recordRequest:', error);
      throw error;
    }
  }

  /**
   * تنسيق وقت الانتظار المتبقي
   * @param waitMinutes عدد الدقائق المتبقية
   * @returns نص منسق للوقت المتبقي
   */
  static formatWaitTime(waitMinutes: number): string {
    if (waitMinutes <= 0) {
      return '';
    }

    if (waitMinutes < 60) {
      const minutes = Math.ceil(waitMinutes);
      return `${minutes} دقيقة`;
    }

    const hours = Math.floor(waitMinutes / 60);
    const remainingMinutes = Math.ceil(waitMinutes % 60);
    
    if (hours >= 24) {
      const days = Math.floor(hours / 24);
      const remainingHours = hours % 24;
      
      if (remainingHours === 0) {
        return `${days} يوم`;
      }
      return `${days} يوم و ${remainingHours} ساعة`;
    }

    if (remainingMinutes === 0) {
      return `${hours} ساعة`;
    }
    
    return `${hours} ساعة و ${remainingMinutes} دقيقة`;
  }

  /**
   * الحصول على رسالة توضيحية للمستخدم حول حالة الحدود
   * @param result نتيجة فحص الحدود
   * @returns رسالة توضيحية
   */
  static getStatusMessage(result: RateLimitCheckResult): string {
    if (result.allowed) {
      const remaining = result.daily_limit - result.daily_requests_count;
      return `يمكنك إرسال ${remaining} طلب${remaining > 1 ? 'ات' : ''} إضافي${remaining > 1 ? 'ة' : ''} اليوم`;
    }

    if (result.blocked_until) {
      const waitTime = this.formatWaitTime(result.wait_minutes);
      return `تم استنفاد المحاولات اليومية (3 طلبات). يرجى الانتظار ${waitTime} قبل المحاولة مرة أخرى`;
    }

    if (result.wait_minutes > 0) {
      const waitTime = this.formatWaitTime(result.wait_minutes);
      return `يجب الانتظار ${waitTime} قبل إرسال طلب آخر`;
    }

    return result.message;
  }

  /**
   * فحص ما إذا كان المستخدم في فترة انتظار
   * @param result نتيجة فحص الحدود
   * @returns true إذا كان في فترة انتظار
   */
  static isInWaitPeriod(result: RateLimitCheckResult): boolean {
    return !result.allowed && result.wait_minutes > 0;
  }

  /**
   * فحص ما إذا كان المستخدم محظور لمدة 24 ساعة
   * @param result نتيجة فحص الحدود
   * @returns true إذا كان محظور
   */
  static isBlocked(result: RateLimitCheckResult): boolean {
    return !result.allowed && result.blocked_until !== null;
  }

  /**
   * الحصول على معلومات الاستخدام الحالي
   * @param result نتيجة فحص الحدود
   * @returns معلومات الاستخدام
   */
  static getUsageInfo(result: RateLimitCheckResult): {
    used: number;
    limit: number;
    remaining: number;
    percentage: number;
  } {
    const used = result.daily_requests_count;
    const limit = result.daily_limit;
    const remaining = Math.max(0, limit - used);
    const percentage = Math.round((used / limit) * 100);

    return {
      used,
      limit,
      remaining,
      percentage
    };
  }
}

export default ContactUpdateRateLimitService;
